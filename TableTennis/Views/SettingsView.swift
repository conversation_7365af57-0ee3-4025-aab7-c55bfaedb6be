import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var themeManager: ThemeManager
    @EnvironmentObject var flicManager: FlicManager
    @EnvironmentObject var cloudKitManager: CloudKitManager
    @EnvironmentObject var joinRequestManager: JoinRequestManager
    @EnvironmentObject var dataManager: DataManager
    @StateObject private var soundManager = SoundManager.shared
    @Environment(\.dismiss) private var dismiss
    @State private var showingFlicSettings = false
    @State private var showingLeaveCompetitionAlert = false
    @State private var showingJoinRequests = false
    @State private var isOwner = false
    @State private var showingDebugStatistics = false

    // App version information
    private var appVersion: String {
        Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
    }

    private var appBuild: String {
        Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "Unknown"
    }

    var body: some View {
        NavigationStack {
            List {
                // Thema sectie
                Section {
                    ForEach(AppTheme.allCases, id: \.self) { theme in
                        ThemeRow(
                            theme: theme,
                            isSelected: themeManager.currentTheme == theme
                        ) {
                            themeManager.setTheme(theme)
                        }
                    }
                } header: {
                    Label("Theme", systemImage: "paintbrush")
                } footer: {
                    Text("Choose how the app looks. 'System' follows your device settings.")
                }

                // Competitie beheer sectie (alleen voor eigenaren)
                if isOwner, let competition = cloudKitManager.currentCompetition {
                    Section {
                        Button {
                            showingJoinRequests = true
                        } label: {
                            HStack {
                                Image(systemName: "person.badge.plus")
                                    .foregroundColor(.blue)

                                Text("Manage Requests")
                                    .foregroundColor(.primary)

                                Spacer()

                                // Badge voor aantal openstaande aanvragen
                                if joinRequestManager.pendingRequestsCount > 0 {
                                    Text("\(joinRequestManager.pendingRequestsCount)")
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.white)
                                        .frame(minWidth: 20, minHeight: 20)
                                        .background(Color.red)
                                        .clipShape(Circle())
                                        .scaleEffect(joinRequestManager.isLoading ? 0.8 : 1.0)
                                        .animation(.easeInOut(duration: 0.2), value: joinRequestManager.isLoading)
                                }

                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    } header: {
                        Label("Competition Management", systemImage: "crown")
                    } footer: {
                        Text("Manage requests from players who want to join '\(competition.name)'.")
                    }
                }

                // Flic buttons sectie
                Section {
                    Button {
                        showingFlicSettings = true
                    } label: {
                        HStack {
                            Label("Flic Buttons", systemImage: "dot.radiowaves.left.and.right")
                                .foregroundColor(.primary)

                            Spacer()

                            // Status indicator
                            if flicManager.bothButtonsConnected {
                                HStack(spacing: 4) {
                                    Circle()
                                        .fill(.green)
                                        .frame(width: 8, height: 8)
                                    Text("Connected")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            } else {
                                Text("Configure")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(Color.secondary)
                        }
                    }
                } header: {
                    Label("Hardware", systemImage: "gamecontroller")
                } footer: {
                    Text("Connect Flic buttons for quick scoring during live matches.")
                }

                // Mix & Match Announcer Section
                Section {
                    Toggle(isOn: $soundManager.isMixMatchAnnouncerEnabled) {
                        HStack {
                            Image(systemName: "speaker.wave.2")
                                .foregroundColor(.blue)
                                .frame(width: 24)

                            VStack(alignment: .leading, spacing: 2) {
                                Text("Mix & Match Announcer")
                                    .foregroundColor(.primary)

                                Text("Announces next teams after each game")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }

                    // Test button (only show when announcer is enabled)
                    if soundManager.isMixMatchAnnouncerEnabled {
                        Button {
                            testAnnouncer()
                        } label: {
                            HStack {
                                Image(systemName: "play.circle")
                                    .foregroundColor(.green)
                                    .frame(width: 24)

                                Text("Test Announcer")
                                    .foregroundColor(.primary)

                                Spacer()
                            }
                        }
                    }
                } header: {
                    Label("Audio", systemImage: "speaker.wave.2")
                } footer: {
                    Text("When enabled, the app will announce the next team combinations after completing each game in Mix & Match matches.")
                }

                // CloudKit/Competition Section
                Section {
                    if let competition = cloudKitManager.currentCompetition {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Current competition:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(competition.name)
                                .font(.headline)
                        }
                        .padding(.vertical, 4)

                        Button("Leave Competition") {
                            showingLeaveCompetitionAlert = true
                        }
                        .foregroundColor(.red)
                    } else {
                        Text("No competition selected")
                            .foregroundColor(.secondary)
                    }
                } header: {
                    Label("Competition", systemImage: "trophy")
                } footer: {
                    Text("Manage your participation in competitions. You can rejoin later.")
                }

                // Debug sectie (alleen in debug builds)
                #if DEBUG
                Section {
                    Button("Force Recalculate All Statistics") {
                        print("🔧 Manual recalculation triggered from Settings...")
                        dataManager.forceRecalculateAllStatistics()
                    }

                    Button("Check Matches Missing ELO Changes") {
                        print("🔍 Checking matches missing ELO changes...")
                        checkMatchesMissingEloChanges()
                    }

                    Button("Migrate Matches to ELO Changes") {
                        print("🔄 ELO migration triggered from Settings...")
                        dataManager.migrateExistingMatchesToEloChanges()
                    }

                    NavigationLink {
                        DebugStatisticsView()
                    } label: {
                        Label("Debug Statistics", systemImage: "ladybug")
                    }
                } header: {
                    Label("Debug", systemImage: "wrench.and.screwdriver")
                } footer: {
                    Text("Debug tools for troubleshooting statistics issues. 'Migrate Matches' adds ELO change data to existing matches. Check Xcode console for output.")
                }
                #endif

                // App informatie sectie
                Section {
                    HStack {
                        Label("Version", systemImage: "info.circle")
                        Spacer()
                        Text(appVersion)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Label("Build", systemImage: "hammer")
                        Spacer()
                        Text(appBuild)
                            .foregroundColor(.secondary)
                    }
                } header: {
                    Label("App Information", systemImage: "app.badge")
                }
                
                // Over sectie
                Section {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Table Tennis Tracker")
                            .font(.headline)
                        
                        Text("A simple app for tracking table tennis matches, scores and ELO ratings.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                } header: {
                    Label("About", systemImage: "questionmark.circle")
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                checkIfOwner()
                // Refresh de join request count wanneer settings wordt geopend
                joinRequestManager.refreshCount()
            }
            .alert("Competitie Verlaten", isPresented: $showingLeaveCompetitionAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Leave", role: .destructive) {
                    cloudKitManager.clearSavedCompetition()
                }
            } message: {
                Text("Are you sure you want to leave the current competition? You can rejoin later.")
            }
        }
        .sheet(isPresented: $showingFlicSettings) {
            FlicSettingsView()
        }
        .sheet(isPresented: $showingJoinRequests) {
            if let competition = cloudKitManager.currentCompetition {
                CompetitionJoinRequestsView(competition: competition)
            }
        }
    }

    private func checkIfOwner() {
        guard let competition = cloudKitManager.currentCompetition else {
            isOwner = false
            return
        }

        Task {
            let ownerStatus = await cloudKitManager.isCurrentUserOwner(of: competition)
            await MainActor.run {
                self.isOwner = ownerStatus
            }
        }
    }

    private func testAnnouncer() {
        // Create test players for the announcement
        let testPlayer1 = Player(name: "Alice", competitionId: UUID())
        let testPlayer2 = Player(name: "Bob", competitionId: UUID())
        let testPlayer3 = Player(name: "Charlie", competitionId: UUID())
        let testPlayer4 = Player(name: "Diana", competitionId: UUID())

        let team1Players = [testPlayer1, testPlayer2]
        let team2Players = [testPlayer3, testPlayer4]

        print("🎙️ Testing announcer with test players")
        soundManager.announceMixMatchTeams(team1Players: team1Players, team2Players: team2Players)
    }

    private func checkMatchesMissingEloChanges() {
        let completedMatches = dataManager.matches.filter { $0.status == .completed }
        let matchesWithoutEloChanges = completedMatches.filter { $0.eloChanges.isEmpty }

        print("🔍 MATCHES MISSING ELO CHANGES:")
        print("📊 Total completed matches: \(completedMatches.count)")
        print("❌ Matches without ELO changes: \(matchesWithoutEloChanges.count)")

        for match in matchesWithoutEloChanges {
            let typeString = match.type == .singles ? "Singles" : (match.type == .doubles ? "Doubles" : "Mix & Match")
            let playersString = match.allPlayers.map { $0.name }.joined(separator: " vs ")
            print("   - \(typeString): \(playersString) (completed: \(match.completedAt?.formatted() ?? "unknown"))")
        }

        if matchesWithoutEloChanges.isEmpty {
            print("✅ All completed matches have ELO changes stored!")
        } else {
            print("⚠️  Run 'Migrate Matches to ELO Changes' to fix missing ELO changes")
        }
    }
}

struct ThemeRow: View {
    let theme: AppTheme
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: theme.icon)
                    .foregroundColor(iconColor)
                    .frame(width: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(theme.displayName)
                        .foregroundColor(.primary)
                    
                    Text(themeDescription)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var iconColor: Color {
        switch theme {
        case .light:
            return .orange
        case .dark:
            return .indigo
        case .system:
            return .blue
        }
    }
    
    private var themeDescription: String {
        switch theme {
        case .light:
            return "Always light mode"
        case .dark:
            return "Always dark mode"
        case .system:
            return "Follows device settings"
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(ThemeManager())
        .environmentObject(FlicManager())
        .environmentObject(CloudKitManager.shared)
}
